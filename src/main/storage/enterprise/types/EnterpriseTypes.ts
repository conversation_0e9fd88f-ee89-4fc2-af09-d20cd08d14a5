/**
 * 企业资源同步系统 - 核心类型定义
 * 功能：定义企业资源管理、配置后端、权限控制等核心类型
 * 依赖：现有StorageTypes
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { StorageType, StorageConfig, SyncResult, FileMetadata } from '../../multi_sync/types/StorageTypes'

/**
 * 企业存储类型枚举（扩展现有StorageType）
 */
export enum EnterpriseStorageType {
  DATABASE = 'database',
  CONSUL = 'consul',
  // 继承现有类型
  SMB = 'smb',
  SFTP = 'sftp',
  GITHUB = 'github',
  MINIO = 'minio',
  ONEDRIVE = 'onedrive'
}

/**
 * 用户角色枚举
 */
export enum UserRole {
  PERSONAL = 'personal',
  ENTERPRISE_ADMIN = 'enterprise_admin',
  SYSTEM_ADMIN = 'system_admin'
}

/**
 * 资源类型枚举
 */
export enum ResourceType {
  HOST = 'host',
  KEYCHAIN = 'keychain',
  SNIPPET = 'snippet'
}

/**
 * 环境类型枚举
 */
export enum Environment {
  PRODUCTION = 'production',
  STAGING = 'staging',
  DEVELOPMENT = 'development'
}

/**
 * 数据库配置接口
 */
export interface DatabaseConfig {
  database_type: 'postgresql' | 'mysql'
  connection_string: string
  table_name: string
  ssl_mode?: 'require' | 'prefer' | 'disable'
  pool_size?: number
  timeout?: number
}

/**
 * Consul配置接口
 */
export interface ConsulConfig {
  host: string
  port?: number
  key_prefix: string
  token?: string
  datacenter?: string
  scheme?: 'http' | 'https'
}

/**
 * 企业存储配置联合类型
 */
export type EnterpriseStorageConfig = DatabaseConfig | ConsulConfig | StorageConfig

/**
 * 企业配置后端接口
 */
export interface EnterpriseConfigBackend {
  id: string
  type: EnterpriseStorageType
  name: string
  priority: number // 优先级，用于故障转移
  enabled: boolean
  config: EnterpriseStorageConfig
  health_status: 'healthy' | 'unhealthy' | 'unknown'
  last_health_check?: Date
  created_at: Date
  updated_at: Date
}

/**
 * 企业资源配置接口
 */
export interface EnterpriseResource {
  id: string
  backend_id: string
  resource_type: ResourceType
  name: string
  config_data: any
  environment: Environment
  tags: string[]
  version: number
  checksum?: string
  created_at: Date
  updated_at: Date
}

/**
 * 用户信息接口
 */
export interface User {
  id: string
  username: string
  email: string
  ldap_dn?: string
  role_type: UserRole
  is_active: boolean
  last_login_at?: Date
  created_at: Date
  updated_at: Date
}

/**
 * 权限接口
 */
export interface Permission {
  resource: string
  action: string
  allowed: boolean
  conditions?: Record<string, any>
}

/**
 * 角色权限接口
 */
export interface RolePermissions {
  role: UserRole
  permissions: Permission[]
}

/**
 * 企业同步结果接口
 */
export interface EnterpriseSyncResult extends SyncResult {
  backend_id: string
  backend_name: string
  sync_type: 'full' | 'incremental' | 'health_check'
  duration_ms: number
  resources_synced: number
}

/**
 * 同步日志接口
 */
export interface SyncLog {
  id: string
  backend_id: string
  operation_type: 'sync' | 'health_check' | 'failover'
  status: 'success' | 'failed' | 'partial'
  result_data?: any
  error_message?: string
  duration_ms?: number
  created_at: Date
}

/**
 * 故障转移配置接口
 */
export interface FailoverConfig {
  enabled: boolean
  max_retries: number
  retry_delay_ms: number
  health_check_interval_ms: number
  failover_threshold: number // 连续失败次数阈值
}

/**
 * 企业同步配置接口
 */
export interface EnterpriseSyncConfig {
  sync_interval_ms: number
  batch_size: number
  conflict_resolution: 'server_wins' | 'client_wins' | 'manual'
  enable_encryption: boolean
  failover: FailoverConfig
}

/**
 * 配置验证结果接口
 */
export interface ConfigValidationResult {
  valid: boolean
  errors: string[]
  warnings?: string[]
}

/**
 * 健康检查结果接口
 */
export interface HealthCheckResult {
  backend_id: string
  healthy: boolean
  latency_ms?: number
  error_message?: string
  checked_at: Date
}

/**
 * 企业资源查询过滤器接口
 */
export interface EnterpriseResourceFilter {
  resource_type?: ResourceType
  environment?: Environment
  tags?: string[]
  backend_id?: string
  search_term?: string
  limit?: number
  offset?: number
}

/**
 * 企业资源查询结果接口
 */
export interface EnterpriseResourceQueryResult {
  resources: EnterpriseResource[]
  total_count: number
  has_more: boolean
}

/**
 * 资源分离策略接口
 */
export interface ResourceSeparationStrategy {
  personal_backends: string[] // 个人资源使用的后端ID列表
  enterprise_backends: string[] // 企业资源使用的后端ID列表
  isolation_mode: 'strict' | 'hybrid' // 严格隔离或混合模式
}

/**
 * 同步状态枚举
 */
export enum SyncStatus {
  IDLE = 'idle',
  SYNCING = 'syncing',
  SUCCESS = 'success',
  FAILED = 'failed',
  CONFLICT = 'conflict'
}

/**
 * 企业同步进度接口
 */
export interface EnterpriseSyncProgress {
  backend_id: string
  status: SyncStatus
  progress: number // 0-100
  current_operation?: string
  total_resources: number
  processed_resources: number
  message?: string
  error?: string
  started_at?: Date
  estimated_completion?: Date
}

/**
 * LDAP配置接口
 */
export interface LDAPConfig {
  enabled: boolean
  server: string
  port?: number
  base_dn: string
  bind_dn?: string
  bind_password?: string
  user_search_filter: string
  group_search_filter?: string
  tls_enabled?: boolean
}

/**
 * 认证结果接口
 */
export interface AuthResult {
  success: boolean
  user?: User
  token?: string
  expires_at?: Date
  error_message?: string
}

/**
 * 审计日志接口
 */
export interface AuditLog {
  id: string
  user_id: string
  action: string
  resource_type: string
  resource_id?: string
  details?: Record<string, any>
  ip_address?: string
  user_agent?: string
  created_at: Date
}

/**
 * 企业配置管理器接口
 */
export interface IEnterpriseConfigManager {
  /**
   * 添加配置后端
   * @param config 后端配置
   * @returns 配置ID
   */
  addBackend(config: Omit<EnterpriseConfigBackend, 'id' | 'created_at' | 'updated_at'>): Promise<string>

  /**
   * 更新配置后端
   * @param id 配置ID
   * @param updates 更新内容
   * @returns 是否成功
   */
  updateBackend(id: string, updates: Partial<EnterpriseConfigBackend>): Promise<boolean>

  /**
   * 删除配置后端
   * @param id 配置ID
   * @returns 是否成功
   */
  removeBackend(id: string): Promise<boolean>

  /**
   * 获取所有配置后端
   * @returns 配置后端列表
   */
  getAllBackends(): Promise<EnterpriseConfigBackend[]>

  /**
   * 获取启用的配置后端（按优先级排序）
   * @returns 启用的配置后端列表
   */
  getEnabledBackends(): Promise<EnterpriseConfigBackend[]>

  /**
   * 测试后端连接
   * @param id 配置ID
   * @returns 健康检查结果
   */
  testBackendConnection(id: string): Promise<HealthCheckResult>

  /**
   * 验证后端配置
   * @param type 后端类型
   * @param config 配置内容
   * @returns 验证结果
   */
  validateBackendConfig(type: EnterpriseStorageType, config: EnterpriseStorageConfig): ConfigValidationResult
}
