/**
 * 智能命令补全增强器
 * 基于上下文、历史和文件系统的智能建议
 * 集成增强命令历史服务
 */

import { EnhancedCommandHistoryItem, CommandCategory } from '@/types/enhanced-command-history'

// 定义 Electron API 接口
interface ElectronAPI {
  ipcRenderer: {
    invoke: (channel: string, data?: unknown) => Promise<unknown>
  }
}

// 定义搜索结果类型
interface SearchResult {
  success: boolean
  data?: EnhancedCommandHistoryItem[]
}

declare global {
  interface Window {
    electron?: ElectronAPI
  }
}

export interface CompletionContext {
  currentDirectory: string
  recentCommands: string[]
  fileSystem: FileSystemInfo
  environment: EnvironmentInfo
}

export interface FileSystemInfo {
  files: string[]
  directories: string[]
  permissions: Record<string, string>
}

export interface EnvironmentInfo {
  shell: string
  os: string
  user: string
  variables: Record<string, string>
}

export interface SmartSuggestion {
  command: string
  description: string
  category: 'command' | 'file' | 'option' | 'template' | 'history'
  confidence: number
  context?: string
  examples?: string[]
}

export interface CompletionTemplate {
  name: string
  pattern: RegExp
  generator: (match: RegExpMatchArray, context: CompletionContext) => SmartSuggestion[]
}

export class SmartCompletionEngine {
  private templates: CompletionTemplate[] = []
  private commandHistory: string[] = []
  private maxHistorySize = 1000
  private currentIp: string = ''

  constructor() {
    this.initializeTemplates()
  }

  /**
   * 设置当前连接的IP地址
   */
  setCurrentIp(ip: string): void {
    this.currentIp = ip
  }

  /**
   * 初始化补全模板
   */
  private initializeTemplates(): void {
    this.templates = [
      // Git 命令补全
      {
        name: 'git-commands',
        pattern: /^git\s+(\w*)$/,
        generator: (match, _context) => {
          void _context // 标记参数为有意未使用
          const partial = match[1] || ''
          const gitCommands = [
            { cmd: 'add', desc: '添加文件到暂存区' },
            { cmd: 'commit', desc: '提交更改' },
            { cmd: 'push', desc: '推送到远程仓库' },
            { cmd: 'pull', desc: '从远程仓库拉取' },
            { cmd: 'status', desc: '查看仓库状态' },
            { cmd: 'log', desc: '查看提交历史' },
            { cmd: 'branch', desc: '分支管理' },
            { cmd: 'checkout', desc: '切换分支或恢复文件' },
            { cmd: 'merge', desc: '合并分支' },
            { cmd: 'diff', desc: '查看差异' }
          ]

          return gitCommands
            .filter(({ cmd }) => cmd.startsWith(partial))
            .map(({ cmd, desc }) => ({
              command: `git ${cmd}`,
              description: desc,
              category: 'command' as const,
              confidence: 0.9,
              examples: this.getGitExamples(cmd)
            }))
        }
      },

      // 文件操作补全
      {
        name: 'file-operations',
        pattern: /^(ls|cat|vim|nano|less|more|head|tail)\s+(.*)$/,
        generator: (match, context) => {
          const command = match[1]
          const partial = match[2] || ''

          const files = context.fileSystem.files.filter((f) => f.startsWith(partial))
          const dirs = context.fileSystem.directories.filter((d) => d.startsWith(partial))

          const suggestions: SmartSuggestion[] = []

          // 文件建议
          files.forEach((file) => {
            suggestions.push({
              command: `${command} ${file}`,
              description: `${this.getCommandDescription(command)} ${file}`,
              category: 'file',
              confidence: 0.8,
              context: `文件: ${file}`
            })
          })

          // 目录建议
          dirs.forEach((dir) => {
            suggestions.push({
              command: `${command} ${dir}/`,
              description: `${this.getCommandDescription(command)} ${dir}/`,
              category: 'file',
              confidence: 0.7,
              context: `目录: ${dir}`
            })
          })

          return suggestions
        }
      },

      // Docker 命令补全
      {
        name: 'docker-commands',
        pattern: /^docker\s+(\w*)$/,
        generator: (match, _context) => {
          void _context // 标记参数为有意未使用
          const partial = match[1] || ''
          const dockerCommands = [
            { cmd: 'run', desc: '运行容器' },
            { cmd: 'build', desc: '构建镜像' },
            { cmd: 'ps', desc: '列出容器' },
            { cmd: 'images', desc: '列出镜像' },
            { cmd: 'pull', desc: '拉取镜像' },
            { cmd: 'push', desc: '推送镜像' },
            { cmd: 'stop', desc: '停止容器' },
            { cmd: 'start', desc: '启动容器' },
            { cmd: 'restart', desc: '重启容器' },
            { cmd: 'rm', desc: '删除容器' },
            { cmd: 'rmi', desc: '删除镜像' },
            { cmd: 'exec', desc: '在容器中执行命令' },
            { cmd: 'logs', desc: '查看容器日志' }
          ]

          return dockerCommands
            .filter(({ cmd }) => cmd.startsWith(partial))
            .map(({ cmd, desc }) => ({
              command: `docker ${cmd}`,
              description: desc,
              category: 'command' as const,
              confidence: 0.9,
              examples: this.getDockerExamples(cmd)
            }))
        }
      },

      // 系统管理命令
      {
        name: 'system-commands',
        pattern: /^(systemctl|service)\s+(\w*)\s*(\w*)$/,
        generator: (match, _context) => {
          void _context // 标记参数为有意未使用
          const tool = match[1]
          const action = match[2] || ''
          const service = match[3] || ''

          const actions = ['start', 'stop', 'restart', 'status', 'enable', 'disable']
          const commonServices = ['nginx', 'apache2', 'mysql', 'postgresql', 'redis', 'docker', 'ssh']

          const suggestions: SmartSuggestion[] = []

          if (!action) {
            // 建议操作
            actions.forEach((act) => {
              suggestions.push({
                command: `${tool} ${act}`,
                description: `${act} 服务`,
                category: 'command',
                confidence: 0.8
              })
            })
          } else if (!service && actions.includes(action)) {
            // 建议服务名
            commonServices.forEach((svc) => {
              suggestions.push({
                command: `${tool} ${action} ${svc}`,
                description: `${action} ${svc} 服务`,
                category: 'command',
                confidence: 0.7
              })
            })
          }

          return suggestions
        }
      },

      // 网络命令补全
      {
        name: 'network-commands',
        pattern: /^(curl|wget)\s+(.*)$/,
        generator: (match, _context) => {
          void _context // 标记参数为有意未使用
          const command = match[1]

          const suggestions: SmartSuggestion[] = []

          if (command === 'curl') {
            suggestions.push(
              {
                command: 'curl -X GET',
                description: 'GET 请求',
                category: 'template',
                confidence: 0.8,
                examples: ['curl -X GET https://api.example.com/users']
              },
              {
                command: 'curl -X POST -H "Content-Type: application/json" -d',
                description: 'POST JSON 请求',
                category: 'template',
                confidence: 0.8,
                examples: ['curl -X POST -H "Content-Type: application/json" -d \'{"key":"value"}\' https://api.example.com/data']
              }
            )
          }

          return suggestions
        }
      }
    ]
  }

  /**
   * 获取智能建议
   */
  async getSuggestions(input: string, context: CompletionContext): Promise<SmartSuggestion[]> {
    const suggestions: SmartSuggestion[] = []

    // 1. 基于模板的建议
    for (const template of this.templates) {
      const match = input.match(template.pattern)
      if (match) {
        const templateSuggestions = template.generator(match, context)
        suggestions.push(...templateSuggestions)
      }
    }

    // 2. 增强历史命令建议（优先级最高）
    const enhancedHistorySuggestions = await this.getEnhancedHistorySuggestions(input)
    suggestions.push(...enhancedHistorySuggestions)

    // 3. 传统历史命令建议
    const historySuggestions = this.getHistorySuggestions(input)
    suggestions.push(...historySuggestions)

    // 4. 智能路径补全
    const pathSuggestions = this.getPathSuggestions(input, context)
    suggestions.push(...pathSuggestions)

    // 5. 常用命令模板
    const templateSuggestions = this.getTemplateSuggestions(input)
    suggestions.push(...templateSuggestions)

    // 按置信度排序并去重
    return this.deduplicateAndSort(suggestions)
  }

  /**
   * 获取增强历史命令建议
   */
  private async getEnhancedHistorySuggestions(input: string): Promise<SmartSuggestion[]> {
    if (!this.currentIp || !input.trim()) {
      return []
    }

    try {
      // 调用增强命令历史服务进行智能搜索
      const searchResult = (await window.electron?.ipcRenderer.invoke('enhanced-command-history:search', {
        ip: this.currentIp,
        query: input.trim(),
        limit: 10
      })) as SearchResult

      if (!searchResult?.success || !searchResult.data) {
        return []
      }

      return searchResult.data.map((item: EnhancedCommandHistoryItem) => ({
        command: item.command,
        description: this.getEnhancedCommandDescription(item),
        category: 'history' as const,
        confidence: this.calculateEnhancedConfidence(item, input),
        context: `${item.category} • 使用 ${item.usage_count} 次`,
        examples: item.tags.length > 0 ? [`标签: ${item.tags.join(', ')}`] : undefined
      }))
    } catch (error) {
      console.warn('获取增强历史命令建议失败:', error)
      return []
    }
  }

  /**
   * 获取传统历史命令建议
   */
  private getHistorySuggestions(input: string): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = []
    const inputLower = input.toLowerCase()

    // 查找匹配的历史命令
    const matches = this.commandHistory.filter((cmd) => cmd.toLowerCase().includes(inputLower)).slice(-10) // 最近10个

    matches.forEach((cmd) => {
      suggestions.push({
        command: cmd,
        description: '历史命令',
        category: 'history',
        confidence: 0.6,
        context: '来自命令历史'
      })
    })

    return suggestions
  }

  /**
   * 获取路径建议
   */
  private getPathSuggestions(input: string, context: CompletionContext): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = []

    // 检查是否包含路径
    const pathMatch = input.match(/(\S+\/\S*)$/)
    if (!pathMatch) return suggestions

    const pathPart = pathMatch[1]
    const basePath = pathPart.substring(0, pathPart.lastIndexOf('/') + 1)
    const fileName = pathPart.substring(pathPart.lastIndexOf('/') + 1)

    // 基于文件系统信息提供建议
    context.fileSystem.files
      .filter((f) => f.startsWith(fileName))
      .forEach((file) => {
        suggestions.push({
          command: input.replace(pathPart, basePath + file),
          description: `文件: ${file}`,
          category: 'file',
          confidence: 0.7
        })
      })

    context.fileSystem.directories
      .filter((d) => d.startsWith(fileName))
      .forEach((dir) => {
        suggestions.push({
          command: input.replace(pathPart, basePath + dir + '/'),
          description: `目录: ${dir}`,
          category: 'file',
          confidence: 0.7
        })
      })

    return suggestions
  }

  /**
   * 获取模板建议
   */
  private getTemplateSuggestions(input: string): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = []

    // 常用命令模板
    const templates = [
      {
        trigger: 'find',
        command: 'find . -name "*.txt" -type f',
        description: '查找文件模板',
        examples: ['find . -name "*.js" -type f', 'find /path -name "pattern" -type d']
      },
      {
        trigger: 'grep',
        command: 'grep -r "pattern" .',
        description: '递归搜索模板',
        examples: ['grep -r "TODO" .', 'grep -i "error" /var/log/']
      },
      {
        trigger: 'tar',
        command: 'tar -czf archive.tar.gz directory/',
        description: '压缩文件模板',
        examples: ['tar -xzf archive.tar.gz', 'tar -tf archive.tar.gz']
      }
    ]

    templates
      .filter((t) => input.startsWith(t.trigger))
      .forEach((t) => {
        suggestions.push({
          command: t.command,
          description: t.description,
          category: 'template',
          confidence: 0.5,
          examples: t.examples
        })
      })

    return suggestions
  }

  /**
   * 去重并排序
   */
  private deduplicateAndSort(suggestions: SmartSuggestion[]): SmartSuggestion[] {
    const seen = new Set<string>()
    const unique = suggestions.filter((s) => {
      if (seen.has(s.command)) return false
      seen.add(s.command)
      return true
    })

    return unique.sort((a, b) => b.confidence - a.confidence)
  }

  /**
   * 添加到历史
   */
  addToHistory(command: string): void {
    if (command.trim()) {
      this.commandHistory.push(command.trim())
      if (this.commandHistory.length > this.maxHistorySize) {
        this.commandHistory.shift()
      }
      // 同时添加到增强命令历史服务
      this.addToEnhancedHistory(command.trim())
    }
  }

  /**
   * 添加命令到增强历史服务
   */
  private async addToEnhancedHistory(command: string): Promise<void> {
    if (!this.currentIp || !command.trim()) {
      return
    }

    try {
      await window.electron?.ipcRenderer.invoke('enhanced-command-history:add', {
        ip: this.currentIp,
        command: command.trim()
      })
    } catch (error) {
      console.warn('添加到增强命令历史失败:', error)
    }
  }

  /**
   * 获取增强命令描述
   */
  private getEnhancedCommandDescription(item: EnhancedCommandHistoryItem): string {
    const categoryMap: Record<CommandCategory, string> = {
      [CommandCategory.SYSTEM]: '系统管理',
      [CommandCategory.FILE]: '文件操作',
      [CommandCategory.NETWORK]: '网络工具',
      [CommandCategory.DEVELOPMENT]: '开发工具',
      [CommandCategory.DATABASE]: '数据库',
      [CommandCategory.DOCKER]: 'Docker',
      [CommandCategory.GIT]: '版本控制',
      [CommandCategory.PACKAGE_MANAGER]: '包管理',
      [CommandCategory.TEXT_PROCESSING]: '文本处理',
      [CommandCategory.MONITORING]: '系统监控',
      [CommandCategory.SECURITY]: '安全工具',
      [CommandCategory.OTHER]: '其他命令'
    }

    return categoryMap[item.category] || '历史命令'
  }

  /**
   * 计算增强命令的置信度
   */
  private calculateEnhancedConfidence(item: EnhancedCommandHistoryItem, input: string): number {
    let confidence = 0.7 // 基础置信度

    // 根据使用频率调整
    if (item.usage_count > 10) confidence += 0.2
    else if (item.usage_count > 5) confidence += 0.1

    // 根据最近使用时间调整
    const daysSinceLastUse = (Date.now() - new Date(item.last_used).getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceLastUse < 1) confidence += 0.1
    else if (daysSinceLastUse < 7) confidence += 0.05

    // 根据匹配程度调整
    const inputLower = input.toLowerCase()
    const commandLower = item.command.toLowerCase()
    if (commandLower.startsWith(inputLower)) confidence += 0.2
    else if (commandLower.includes(inputLower)) confidence += 0.1

    return Math.min(confidence, 1.0)
  }

  /**
   * 获取命令描述
   */
  private getCommandDescription(command: string): string {
    const descriptions: Record<string, string> = {
      ls: '列出',
      cat: '查看',
      vim: '编辑',
      nano: '编辑',
      less: '分页查看',
      more: '分页查看',
      head: '查看开头',
      tail: '查看结尾'
    }
    return descriptions[command] || '操作'
  }

  /**
   * 获取Git示例
   */
  private getGitExamples(command: string): string[] {
    const examples: Record<string, string[]> = {
      add: ['git add .', 'git add file.txt', 'git add -A'],
      commit: ['git commit -m "message"', 'git commit -am "message"'],
      push: ['git push origin main', 'git push -u origin feature'],
      pull: ['git pull origin main', 'git pull --rebase'],
      status: ['git status', 'git status -s'],
      log: ['git log --oneline', 'git log --graph'],
      branch: ['git branch -a', 'git branch new-feature'],
      checkout: ['git checkout main', 'git checkout -b feature']
    }
    return examples[command] || []
  }

  /**
   * 获取Docker示例
   */
  private getDockerExamples(command: string): string[] {
    const examples: Record<string, string[]> = {
      run: ['docker run -it ubuntu bash', 'docker run -d -p 80:80 nginx'],
      build: ['docker build -t myapp .', 'docker build -f Dockerfile.prod .'],
      ps: ['docker ps', 'docker ps -a'],
      images: ['docker images', 'docker images -a'],
      exec: ['docker exec -it container_id bash', 'docker exec container_id ls']
    }
    return examples[command] || []
  }
}

// 单例实例
export const smartCompletion = new SmartCompletionEngine()
