export default {
  common: {
    language: '简体中文',
    workspace: '工作空间',
    files: '文件管理',
    keychain: '秘钥',
    extensions: '扩展',
    ai: 'AI',
    user: '用户',
    setting: '设置',
    notice: '通知',
    logout: '退出登录',
    login: '登录',
    userInfo: '个人信息',
    userConfig: '设置',
    alias: 'Alias配置',
    assetConfig: '资产管理',
    keyChainConfig: '秘钥管理',
    search: '搜索',
    connect: '连接',
    edit: '编辑',
    delete: '删除',
    save: '保存',
    create: '创建',
    cancel: '取消',
    done: '完成',
    remove: '删除',
    noData: '暂无数据',
    close: '关闭',
    closeOther: '关闭其他',
    closeAll: '全部关闭',
    copy: '复制',
    paste: '粘贴',
    clear: '清除',
    copyWithShortcut: '复制',
    pasteWithShortcut: '粘贴',
    disconnect: '断开连接',
    reconnect: '重新连接',
    newTerminal: '新终端',
    closeTerminal: '关闭终端',
    splitRight: '向右分屏',
    splitDown: '向下分屏',
    clone: '克隆',
    clearTerm: '清屏',
    shrotenName: '缩短主机名',
    fontsize: '字体大小',
    largen: '放大',
    smaller: '缩小',
    globalExecOn: '全局执行命令(已开)',
    globalExec: '全局执行命令',
    syncInputOn: '同步输入(已开)',
    syncInput: '同步输入',
    allExecuted: '全部执行',
    pleaseLoginFirst: '请先登录',
    select: '选择',
    rightArrowKey: '方向右键进入推荐模式',
    reset: '重置',
    confirm: '确认',
    ok: '确定',
    quickCommandOn: '快捷命令(已开)',
    quickCommand: '快捷命令',
    fileManager: '文件管理',
    add: '添加',
    all: '全部',
    refresh: '刷新',
    fullscreen: '全屏',
    exitFullscreen: '退出全屏',
    editFile: '编辑文件：',
    newFile: '新建文件：',
    saveSuccess: '保存成功',
    saveFailed: '保存失败',
    saveError: '保存出错',
    saveConfirmTitle: '保存更改',
    saveConfirmContent: '您想将更改保存到 {filePath} 吗？',
    pleaseInputLabel: '请输入标签名称',
    pleaseInputPrivateKey: '请输入私钥',
    localhost: '本地主机',
    favoriteBar: '收藏栏',
    executeCommandToAllWindows: '执行命令到全部窗口',
    reloadAliasDataFailed: '重新加载别名数据失败',
    personalAssetFavoriteError: '个人资产收藏错误',
    organizationAssetFavoriteError: '组织本身收藏错误',
    updateOrganizationAssetFavoriteMethodNotFound: 'window.api.updateOrganizationAssetFavorite 方法不存在!',
    favoriteStatusUpdateFailed: '收藏状态更新失败',
    updateOrganizationAssetFavoriteError: 'updateOrganizationAssetFavorite 错误',
    refreshFailed: '刷新失败',
    clipboardReadFailed: '无法读取剪贴板内容',
    getUserInfoFailed: '获取用户信息失败',
    updateTerminalStatusError: '更新终端状态时出错',
    sendTerminalStatusError: '发送终端状态时出错',
    getCursorPositionFailed: '获取光标位置失败',
    permissionDenied: '权限被拒绝',
    timeoutGettingAssetInfo: '获取资产信息超时',
    errorGettingAssetInfo: '获取资产信息时出错',
    unknownError: '未知错误',
    badRequest: '请求参数错误',
    unauthorized: '未授权访问',
    forbidden: '访问被禁止',
    notFound: '资源未找到',
    internalServerError: '服务器内部错误',
    badGateway: '网关错误',
    serviceUnavailable: '服务不可用'
  },
  term: {
    welcome: '欢迎使用 Chaterm',
    searchPlaceholder: '搜索终端内容...',
    searchPrevious: '上一个 (Shift+Enter)',
    searchNext: '下一个 (Enter)'
  },
  // 登录相关翻译已屏蔽 - Login related translations disabled
  // login: {
  //   enterprise: '企业版',
  //   personal: '个人版',
  //   contact: '联系我们',
  //   welcome: '欢迎使用',
  //   title: 'Chaterm',
  //   loginByUser: '账号密码登录',
  //   loginByEmail: '邮箱验证码登录',
  //   login: '立即登录',
  //   loggingIn: '正在登录中...',
  //   skip: '暂不登录？',
  //   skipLogin: '跳过',
  //   applyTag: '还没有账号？ ',
  //   apply: '立即申请',
  //   pleaseInputUsername: '请输入账号',
  //   pleaseInputPassword: '请输入密码',
  //   pleaseInputEmail: '请输入邮箱',
  //   pleaseInputCode: '请输入验证码',
  //   invalidEmail: '请输入有效的邮箱地址',
  //   usernameTooltip: '请输入您的账号',
  //   passwordTooltip: '请输入您的密码',
  //   retryAfter: '{seconds}秒后重试',
  //   getCode: '获取验证码',
  //   pleaseInputEmailAndCode: '请输入邮箱和验证码',
  //   codeSent: '验证码已发送',
  //   codeSendFailed: '验证码发送失败',
  //   loginFailed: '登录失败',
  //   externalLoginFailed: '启动外部登录失败',
  //   loginProcessFailed: '登录处理失败',
  //   databaseInitFailed: '数据库初始化失败',
  //   initializationFailed: '初始化失败，请重试',
  //   routeNavigationFailed: '跳转失败，请重试',
  //   operationFailed: '操作失败，请重试',
  //   recordLoginLogFailed: '记录登录日志失败',
  //   recordLoginFailedLogFailed: '记录登录失败日志失败',
  //   guestDatabaseInitFailed: '访客数据库初始化失败',
  //   externalLoginIPDetectionError: '外部登录IP检测异常',
  //   loginPageIPDetectionError: '登录页面IP检测异常',
  //   handleProtocolUrlFailed: '处理协议 URL 失败',
  //   routeJumpFailed: '路由跳转失败',
  //   skipLoginHandleFailed: '跳过登录处理失败',
  //   startExternalLoginFailed: '启动外部登录失败',
  //   databaseInitializationFailed: '数据库初始化失败',
  //   loginHandleFailed: '登录处理失败',
  //   authCallbackDetected: '检测到认证回调 URL',
  //   linuxPlatformHandleAuth: '在 Linux 平台上处理认证回调'
  // },
  login: {
    // 登录功能已屏蔽，保留基本字段以避免错误 - Login functionality disabled, keep basic fields to avoid errors
    welcome: '欢迎使用',
    title: 'Chaterm',
    login: '功能已屏蔽',
    skip: '功能已屏蔽',
    skipLogin: '进入应用'
  },
  workspace: {
    workspace: '工作空间',
    personal: '个人空间',
    searchHost: '搜索主机'
  },
  header: {
    title: 'Chaterm智能终端'
  },
  user: {
    autoCompleteStatus: '自动补全',
    quickVimStatus: '快捷定制化Vim',
    commonVimStatus: '常规定制化Vim',
    aliasStatus: '全局Alias',
    highlightStatus: '全局高亮',
    fontSize: '字体大小(px)',
    fontFamily: '字体',
    cursorStyle: '光标样式',
    scrollBack: '终端回滚量',
    terminalType: '终端类型',
    install: '安装',
    installing: '安装中',
    notInstall: '未安装',
    uninstall: '卸载',
    uninstalling: '卸载中',
    baseSetting: '基础设置',
    terminalSetting: '终端设置',
    ai: 'AI',
    keychain: '秘钥',
    textEditor: '文本编辑器',
    visualVimEditor: '可视化Vim编辑器',
    fileManagerPlugin: '文件管理器扩展',
    fileManagerPluginDescribe: '安装后通过 "鼠标右键-文件管理器" 打开',
    cursorStyleBlock: '块',
    cursorStyleBar: '竖线',
    cursorStyleUnderline: '下划线',
    mouseEvent: '鼠标事件',
    middleMouseEvent: '鼠标中键事件',
    rightMouseEvent: '鼠标右键事件',
    pasteClipboard: '粘贴剪贴板内容',
    showContextMenu: '打开弹出式菜单',
    none: '无',
    terminalBackground: '终端背景',
    terminalBackgroundType: '背景类型',
    // 终端设置相关
    mouseEvents: '鼠标事件设置',
    sshSettings: 'SSH设置',
    sshAgentsStatus: 'SSH代理状态',
    sshAgentConfig: 'SSH代理配置',
    configureAgent: '配置代理',
    saveSettings: '保存设置',
    agentPath: '代理路径',
    agentArgs: '代理参数',
    agentEnv: '环境变量',
    agentPathPlaceholder: '请输入SSH代理程序路径',
    agentArgsPlaceholder: '请输入代理启动参数',
    agentEnvPlaceholder: '请输入环境变量，每行一个，格式：KEY=VALUE',
    terminalConfigSaved: '终端配置已保存',
    terminalConfigSaveFailed: '终端配置保存失败',
    agentConfigSaved: 'SSH代理配置已保存',
    agentConfigSaveFailed: 'SSH代理配置保存失败',
    enabled: '启用',
    disabled: '禁用',
    paste: '粘贴',
    contextMenu: '右键菜单',
    cursorBlock: '块状',
    cursorBar: '竖线',
    cursorUnderline: '下划线',
    terminalBackgroundDefault: '默认',
    terminalBackgroundColor: '纯色背景',
    terminalBackgroundImage: '图片背景',
    terminalBackgroundColorPicker: '背景颜色',
    terminalBackgroundImageUrl: '背景图片URL',
    terminalBackgroundOpacity: '背景透明度',
    terminalBackgroundImageUpload: '上传图片',
    terminalBackgroundImageUrlPlaceholder: '请输入图片URL或上传图片',
    themeColors: '程序外观主题颜色',
    primaryColor: '主色调',
    accentColor: '强调色',
    backgroundColor: '背景色',
    surfaceColor: '表面色',
    textColor: '文字色',
    borderColor: '边框色',
    resetToDefault: '重置为默认',
    applyTheme: '应用主题',
    themeColorsApplied: '主题颜色已应用',
    watermark: '水印',
    watermarkDescribe: '在终端上显示水印',
    watermarkOpen: '开启',
    watermarkClose: '关闭',
    language: '语言',
    theme: '主题',
    themeDark: '暗色',
    themeLight: '亮色',
    themeAuto: '自动',
    telemetry: '遥测',
    telemetryEnabled: '开启',
    telemetryDisabled: '关闭',
    telemetryDescription:
      '通过发送匿名使用数据和错误报告帮助改进 Chaterm。我们绝不会发送任何代码、提示内容或个人信息。如需了解详细信息，请查看我们的<a href="../../../../../PRIVACY.md" target="_blank" rel="noopener noreferrer">隐私政策</a>。',
    telemetryDescriptionText:
      '通过发送匿名使用数据和错误报告帮助改进 Chaterm。我们绝不会发送任何代码、提示内容或个人信息。如需了解详细信息，请查看我们的',
    privacyPolicy: '隐私政策',
    enterprise: '企业用户',
    personal: '个人用户',
    name: '名称',
    email: '邮箱',
    mobile: '手机',
    organization: '组织',
    ip: 'IP地址',
    macAddress: 'MAC地址',
    general: '通用',
    extensions: '扩展',
    about: '关于',
    privacy: '隐私',
    secretRedaction: '密文脱敏',
    secretRedactionDescription: '密文脱敏功能旨在自动从您的AI对话框输出中遮盖密码、IP 地址、API 密钥和个人身份信息等秘密和敏感信息。',
    secretRedactionEnabled: '开启',
    secretRedactionDisabled: '关闭',
    supportedPatterns: '支持的正则表达式模式',
    ipv4Address: 'IPv4 地址',
    ipv6Address: 'IPv6 地址',
    slackAppToken: 'Slack 应用令牌',
    phoneNumber: '电话号码',
    awsAccessId: 'AWS 访问 ID',
    googleApiKey: 'Google API 密钥',
    googleOAuthId: 'Google OAuth ID',
    githubClassicPersonalAccessToken: 'GitHub 经典个人访问令牌',
    githubFineGrainedPersonalAccessToken: 'GitHub 精细粒度个人访问令牌',
    githubOAuthAccessToken: 'GitHub OAuth 访问令牌',
    githubUserToServerToken: 'GitHub 用户到服务器令牌',
    githubServerToServerToken: 'GitHub 服务器到服务器令牌',
    stripeKey: 'Stripe 密钥',
    firebaseAuthDomain: 'Firebase 认证域名',
    jsonWebToken: 'JWT 令牌',
    openaiApiKey: 'OpenAI API 密钥',
    anthropicApiKey: 'Anthropic API 密钥',
    fireworksApiKey: 'Fireworks API 密钥',
    dataSync: '数据同步',
    dataSyncDescription: '数据同步旨在将用户配置的资产、密钥等信息进行跨设备同步',
    dataSyncEnabled: '开启',
    dataSyncDisabled: '关闭',
    shortcuts: '快捷键',
    shortcutSettings: '快捷键设置',
    shortcutDescription: '自定义应用程序快捷键',
    rules: '规则',
    userRules: '用户规则',
    userRulesDescription: '管理您的自定义用户规则和偏好设置',
    addRule: '添加规则',
    noRulesYet: '暂无规则',
    noRulesDescription: '为Agent添加规则和偏好设置',
    rulePlaceholder: '样式要求、响应语言、语调等...',
    shortcutAction: '动作',
    shortcutKey: '快捷键',
    shortcutModify: '修改',
    shortcutReset: '重置',
    shortcutSave: '保存',
    shortcutCancel: '取消',
    shortcutConflict: '快捷键冲突',
    shortcutConflictMessage: '此快捷键已被占用，请选择其他快捷键',
    shortcutInvalid: '无效的快捷键',
    shortcutInvalidMessage: '请输入有效的快捷键组合',
    shortcutSaveSuccess: '快捷键保存成功',
    shortcutSaveFailed: '快捷键保存失败',
    shortcutResetSuccess: '快捷键重置成功',
    shortcutPressKeys: '请按下快捷键组合',
    shortcutRecording: '录制中...',
    shortcutClickToModify: '点击修改',
    model: '模型',
    enableExtendedThinking: '开启扩展思考',
    enableExtendedThinkingDescribe: '提高预算可实现更全面、更细致的推理',
    autoApproval: '开启自动执行',
    autoApprovalDescribe: '允许在不要求确认的情况下运行工具',
    features: '特征',
    enableCheckpoints: '开启检查点',
    enableCheckpointsDescribe: '允许在整个任务中保存工作区的检查点',
    openAIReasoningEffort: 'OpenAI推理工作',
    openAIReasoningEffortLow: '低',
    openAIReasoningEffortMedium: '中',
    openAIReasoningEffortHigh: '高',
    proxySettings: '代理配置',
    enableProxy: '开启代理',
    proxyType: '协议',
    proxyHost: '主机名',
    noProxyAdd: '暂无已配置代理',
    proxyName: '代理名称',
    pleaseInputProxyName: '请输入代理配置名称',
    pleaseInputOtherProxyName: '代理配置名称已存在，请使用其他名称',
    pleaseInputProxyHost: '请输入代理主机地址',
    errorProxyPort: '端口范围应在 1-65535 之间',
    proxyPort: '端口号',
    enableProxyIdentity: '代理身份认证',
    proxyUsername: '账号',
    proxyPassword: '密码',
    shellIntegrationTimeout: 'Shell集成超时（秒）',
    shellIntegrationTimeoutPh: '请输入超时时间（秒）',
    shellIntegrationTimeoutDescribe: '设置等待shell集成激活的时间',
    terminal: '终端',
    apiConfiguration: 'API 配置',
    apiProvider: 'API 提供商',
    apiProviderDescribe:
      '通过提供上述密钥或使用默认的AWS凭据提供程序（即~/.AWS/rentials或环境变量）进行身份验证。这些凭据仅在本地用于从此客户端发出API请求。',
    awsAccessKey: 'AWS Access Key',
    awsAccessKeyPh: '请输入AWS Access Key',
    awsSecretKey: 'AWS Secret Key',
    awsSecretKeyPh: '请输入AWS Secret Key',
    awsSessionToken: 'AWS Session Token',
    awsSessionTokenPh: '请输入AWS  Session Token',
    awsRegion: 'AWS 区域',
    awsRegionPh: '请选择区域',
    awsEndpointSelected: '使用自定义VPC端点',
    awsBedrockEndpointPh: '输入VPC端点URL（可选）',
    awsUseCrossRegionInference: '使用跨区域推理',
    chatSettings: '模式',
    liteLlmBaseUrl: 'URL地址',
    liteLlmBaseUrlPh: '请输入URL地址',
    liteLlmApiKey: 'API Key',
    liteLlmApiKeyPh: '请输入API Key',
    liteLlmApiKeyDescribe: '密钥存储在本地，仅用于从此客户端发出API请求',
    customInstructions: '自定义指令',
    customInstructionsPh: '这些指令添加到每个请求发送的系统提示的末尾。例如：总是用中文回答',
    deepSeekApiKey: 'DeepSeek API Key',
    deepSeekApiKeyPh: '请输入API Key',
    deepSeekApiKeyDescribe: '此密钥存储在本地，仅用于从此客户端发出API请求',
    openAiBaseUrl: 'OpenAI URL地址',
    openAiBaseUrlPh: '请输入URL地址',
    openAiApiKey: 'OpenAI API Key',
    openAiApiKeyPh: '请输入API Key',
    openAiApiKeyDescribe: '此密钥存储在本地，仅用于从此客户端发出API请求',
    checkModelConfigFailMessage: '缺少必要的模型配置',
    checkModelConfigFailDescription: '请填写必要的模型配置，设置->模型->添加模型->API 配置',
    checkSuccessMessage: '连接成功',
    checkSuccessDescription: 'API密钥有效',
    checkFailMessage: '连接失败',
    checkFailDescriptionDefault: '未知错误',
    checkFailDescriptionMain: '无法连接到主进程',
    models: '模型',
    modelNames: '模型列表',
    aiPreferences: 'AI 偏好设置',
    addModel: '添加模型',
    addModelExistError: '模型名称已存在',
    addModelSuccess: '新增模型成功',
    billing: '计费概览',
    subscription: '订阅类型',
    expires: '过期时间',
    ratio: '用量占比',
    budgetResetAt: '下次重置时间',
    sshAgentSettings: 'SSH Agent设置',
    noKeyAdd: '暂无已添加秘钥',
    remove: '移除',
    comment: '备注',
    fingerprint: '指纹',
    addSuccess: '添加成功',
    addFailed: '添加失败',
    type: '类型',
    loadConfigFailed: '加载配置失败',
    loadConfigFailedDescription: '将使用默认配置',
    telemetryUpdateFailed: '遥测设置更新失败',
    telemetryUpdateFailedDescription: '请稍后重试',
    error: '错误',
    saveConfigFailedDescription: '保存配置失败',
    themeSwitchFailed: '主题切换失败',
    themeSwitchFailedDescription: '请稍后重试',
    saveAliasStatusFailed: '保存别名状态失败',
    saveBedrockConfigFailed: '保存Bedrock配置失败',
    saveLiteLlmConfigFailed: '保存LiteLLM配置失败',
    saveDeepSeekConfigFailed: '保存DeepSeek配置失败',
    saveOpenAiConfigFailed: '保存OpenAI配置失败',
    // Ollama
    ollamaBaseUrl: 'Ollama URL地址',
    ollamaBaseUrlPh: '请输入Ollama服务地址，如：http://localhost:11434',
    ollamaBaseUrlDescribe: 'Ollama本地服务地址，通常为http://localhost:11434',
    ollamaApiKey: 'Ollama API Key',
    ollamaApiKeyPh: '请输入API Key（可选）',
    ollamaApiKeyDescribe: 'Ollama API密钥（可选），本地部署通常不需要',
    saveOllamaConfigFailed: '保存Ollama配置失败',
    // 火山引擎
    volcengineBaseUrl: '火山引擎 URL地址',
    volcengineBaseUrlPh: '请输入火山引擎API地址',
    volcengineApiKey: '火山引擎 API Key',
    volcengineApiKeyPh: '请输入火山引擎API Key',
    volcengineApiKeyDescribe: '此密钥存储在本地，仅用于从此客户端发出API请求',
    saveVolcengineConfigFailed: '保存火山引擎配置失败',
    // 阿里云百炼
    alibabaBaseUrl: '阿里云百炼 URL地址',
    alibabaBaseUrlPh: '请输入阿里云百炼API地址',
    alibabaApiKey: '阿里云百炼 API Key',
    alibabaApiKeyPh: '请输入阿里云百炼API Key',
    alibabaApiKeyDescribe: '此密钥存储在本地，仅用于从此客户端发出API请求',
    saveAlibabaConfigFailed: '保存阿里云百炼配置失败',
    // 智谱清言
    glmBaseUrl: '智谱清言 URL地址',
    glmBaseUrlPh: '请输入智谱清言API地址',
    glmApiKey: '智谱清言 API Key',
    glmApiKeyPh: '请输入智谱清言API Key',
    glmApiKeyDescribe: '此密钥存储在本地，仅用于从此客户端发出API请求',
    saveGlmConfigFailed: '保存智谱清言配置失败',
    // Gemini
    geminiBaseUrl: 'Gemini URL地址',
    geminiBaseUrlPh: '请输入Gemini API地址',
    geminiApiKey: 'Gemini API Key',
    geminiApiKeyPh: '请输入Gemini API Key',
    geminiApiKeyDescribe: '此密钥存储在本地，仅用于从此客户端发出API请求',
    saveGeminiConfigFailed: '保存Gemini配置失败'
  },
  extensions: {
    extensions: '扩展',
    alias: '别名',
    fuzzySearch: '模糊搜索',
    aliasDescription: '全局Alias配置',
    command: '命令',
    action: '操作',
    success: '成功',
    error: '失败',
    errorDescription: '创建失败！',
    errorNetWork: '网络请求异常',
    warning: '提示',
    missingAliasCommand: '缺少别名或命令！',
    deleteSuccess: '删除成功！',
    errorDeletingAlias: '删除失败！',
    aliasAlreadyExists: '别名已存在！',
    addCommand: '添加命令'
  },
  commandDialog: {
    placeholder: '描述您想要执行的操作...',
    generating: '正在生成命令...',
    submit: '生成'
  },
  shortcuts: {
    actions: {
      openSettings: '打开设置',
      toggleLeftSidebar: '切换左侧边栏',
      toggleRightSidebar: '切换右侧边栏',
      sendOrToggleAi: '发送到AI / 切换AI侧边栏',
      switchToNextTab: '切换到下一个标签页',
      switchToPrevTab: '切换到上一个标签页',
      switchToSpecificTab: '切换到指定标签页[1...9]',
      openCommandDialog: '打开AI命令生成对话框',
      newTab: '新Tab页',
      openFileManager: '打开文件管理',
      clearTerminal: '清屏',
      fontSizeIncrease: '字体放大',
      fontSizeDecrease: '字体缩小'
    }
  },
  personal: {
    host: '资产管理',
    newHost: '添加主机',
    keyChain: '密钥',
    address: '连接信息',
    general: '概述',
    group: '分组',
    accountPassword: '账号密码',
    key: '密钥',
    username: '用户名',
    password: '密码',
    remoteHost: '连接IP或地址',
    port: '端口',
    verificationMethod: '认证方式',
    alias: '别名',
    pleaseInputRemoteHost: '请输入远程连接地址',
    pleaseInputPort: '请输入端口',
    pleaseInputUsername: '请输入用户名',
    pleaseInputPassword: '请输入密码',
    pleaseSelectKeychain: '请选择密钥',
    pleaseInputAlias: '请输入别名或主机名',
    pleaseSelectGroup: '请选择分组',
    pleaseSelectSshProxy: '请选择SSH代理',
    proxyConfig: 'SSH代理',
    personal: '个人资源',
    enterprise: '企业资源',
    assetTree: '资产树',
    resourceManagement: '资源管理',
    enterpriseResourceManagement: '企业资源管理',
    editHost: '编辑主机',
    saveAsset: '保存',
    createAsset: '创建',
    deleteConfirm: '删除确认',
    deleteConfirmContent: '确定要删除资产 "{name}" 吗？',
    deleteSuccess: '删除资产 {name} 成功',
    deleteFailure: '删除失败',
    deleteError: '删除出错: {error}',
    createSuccess: '创建资产成功',
    createError: '创建主机失败',
    saveSuccess: '保存成功',
    saveError: '保存出错',
    favoriteUpdateSuccess: '资产 {name} 收藏状态已更新',
    favoriteUpdateFailure: '更新收藏状态失败',
    favoriteUpdateError: '更新收藏状态出错',
    defaultGroup: '主机',
    hostType: 'ssh',
    personalAsset: '个人',
    enterpriseAsset: '企业',
    organizationTip: '仅支持 Jumpserver',
    refreshAssets: '资产',
    refreshingAssets: '正在刷新资产...',
    refreshSuccess: '资产刷新成功',
    refreshError: '资产刷新失败',
    validationRemoteHostRequired: '远程连接地址不能为空',
    validationPortRequired: '端口不能为空',
    validationUsernameRequired: '用户名不能为空',
    validationPasswordRequired: '密码不能为空',
    validationKeychainRequired: '密钥不能为空',
    validationEnterpriseRequiredFields: '企业资产的远程地址、端口、用户名、密码或密钥不能为空',
    import: '导入',
    export: '导出',
    importSuccess: '导入成功',
    importError: '导入失败',
    importFormatError: '文件格式错误，请选择有效的JSON文件',
    importNoData: '没有可导入的数据',
    importSuccessCount: '成功导入 {count} 个资产',
    importErrorCount: '导入失败 {count} 个资产',
    exportSuccess: '成功导出 {count} 个资产',
    exportError: '导出失败',
    exportNoData: '没有可导出的资产数据',
    importHelp: '导入格式说明',
    importFormatTitle: '资产导入格式说明',
    importFormatGuide: '导入格式说明：',
    importFormatStep1: '1. 文件必须是JSON格式',
    importFormatStep2: '2. 数据必须是数组格式',
    importFormatStep3: '3. 每个资产对象必须包含以下字段：',
    importFormatRequired: '必填',
    importFormatOptional: '可选',
    importFormatUsername: '用户名（必填）',
    importFormatIp: 'IP地址（必填）',
    importFormatPassword: '密码（可选，密钥认证时可为空）',
    importFormatLabel: '别名（可选，默认为IP地址）',
    importFormatGroup: '分组（可选，默认为"主机"）',
    importFormatAuthType: '认证方式（可选，"password"或"keyBased"）',
    importFormatKeyChain: '密钥ID（可选，密钥认证时使用）',
    importFormatPort: '端口（可选，默认22）',
    importFormatAssetType: '资产类型（可选，"person"或"organization"）',
    importFormatExample: '示例：',
    comment: '备注',
    addComment: '添加备注',
    editComment: '编辑备注',
    commentPlaceholder: '请输入备注信息',
    commentSaved: '备注保存成功',
    commentSaveFailed: '备注保存失败',
    commentSaveError: '备注保存出错',
    customFolders: '自定义文件夹',
    folder: '文件夹',
    createFolder: '创建文件夹',
    editFolder: '编辑文件夹',
    deleteFolder: '删除文件夹',
    folderName: '文件夹名称',
    folderDescription: '文件夹描述',
    pleaseInputFolderName: '请输入文件夹名称',
    pleaseInputFolderDescription: '请输入文件夹描述（可选）',
    folderCreated: '文件夹创建成功',
    folderUpdated: '文件夹更新成功',
    folderDeleted: '文件夹删除成功',
    folderCreateFailed: '文件夹创建失败',
    folderUpdateFailed: '文件夹更新失败',
    folderDeleteFailed: '文件夹删除失败',
    moveToFolder: '移动到文件夹',
    removeFromFolder: '从文件夹移除',
    assetMoved: '资产移动成功',
    assetRemoved: '资产移除成功',
    assetMoveFailed: '资产移动失败',
    assetRemoveFailed: '资产移除失败',
    selectFolder: '选择文件夹',
    noFolders: '暂无自定义文件夹',
    createFolderFirst: '请先创建文件夹',
    folderDeleteConfirm: '删除文件夹确认',
    folderDeleteConfirmContent: '确定要删除文件夹 "{name}" 吗？删除后文件夹中的资产将回到原位置。',
    folderDeleteConfirmWithAssets: '确定要删除文件夹 "{name}" 吗？该文件夹包含 {count} 个资产，删除后资产将回到原位置。',
    missingAssetId: '缺少资产 ID'
  },
  ai: {
    welcome: '您好，请问需要在终端做什么操作？',
    loginPrompt: '登录后即可使用 AI 功能，新用户注册免费使用两周',
    searchHost: '输入IP搜索',
    noMatchingHosts: '无匹配主机',
    copy: '复制',
    run: '执行',
    reject: '拒绝',
    cancel: '取消',
    resume: '恢复',
    agentMessage: '到任意主机执行命令查询，排查错误和任务处理等任何事情',
    cmdMessage: '到当前活跃终端执行任务，请先连接目标主机',
    chatMessage: '与AI对话，学习，头脑风暴（无法操作服务器）',
    localhost: '本地主机',
    newChat: '新建对话',
    showChatHistory: '显示历史记录',
    closeAiSidebar: '关闭AI侧边栏',
    addHost: '添加主机',
    processing: '处理中...',
    searchHistoryPH: '请输入',
    loading: '加载中...',
    loadMore: '加载',
    copyToClipboard: '已复制到剪贴板',
    retry: '重试',
    taskCompleted: '任务已完成',
    newTask: '开始新任务',
    codePreview: '代码预览 ({lines}行)',
    enterCustomOption: '请输入您的答案...',
    submit: '提交',
    // 语音输入相关
    startVoiceInput: '开始语音输入',
    stopRecording: '停止语音录制',
    startRecording: '开始语音录制',
    recordingTooShort: '录制时间过短',
    recordingTooShortDesc: '请录制更长的语音内容',
    recordingFailed: '录制失败',
    recordingErrorDesc: '录制过程中发生错误',
    recordingTimeLimit: '录制时间到达上限',
    recordingTimeLimitDesc: '已自动停止录制',
    microphonePermissionDenied: '麦克风权限被拒绝，请在浏览器设置中允许麦克风访问',
    microphoneNotFound: '未找到麦克风设备',
    microphoneInUse: '麦克风正在被其他应用程序使用',
    voiceInputFailed: '语音输入失败',
    recordingStopped: '录制已停止',
    processingVoice: '正在处理语音识别...',
    audioFileTooLarge: '音频文件过大，请录制更短的语音（建议不超过60秒，最大{maxSize}MB）',
    audioFormatConversion: '音频格式转换',
    formatConversionDesc: '检测到不支持的格式，已转换为 {format} 格式',
    voiceRecognitionSuccess: '语音识别成功',
    recognitionResult: '识别结果: {text}',
    voiceRecognitionEmpty: '语音识别结果为空',
    recognitionEmptyDesc: '请重新录制或检查语音内容',
    voiceRecognitionFailed: '语音转录失败',
    voiceRecognitionServiceUnavailable: '语音转录服务暂时不可用',
    startRecordingDesc: '请开始说话，录制最长60秒。后端支持格式：{formats}',
    recordingFormat: '使用的格式',
    stopAudioTracks: '停止所有音频轨道',
    startRecordingNote: '开始录制，每100ms收集一次数据',
    websocketConnectionFailed: 'WebSocket连接失败，请检查后端服务',
    uploadFile: '上传文件',
    fileUploadSuccess: '文件上传成功',
    fileUploadSuccessDesc: '已读取文件 "{fileName}" 的内容',
    fileTooLarge: '文件过大',
    fileTooLargeDesc: '文件大小不能超过1MB，请选择较小的文件',
    fileReadFailed: '文件读取失败',
    fileReadErrorDesc: '无法读取文件内容，请检查文件格式是否正确',
    fileContent: '文件内容 ({fileName})',
    sendContentError: '发送内容错误',
    sendContentEmpty: '发送内容为空，请输入内容',
    getAssetInfoFailed: '获取当前资产连接信息失败',
    pleaseConnectAsset: '请先建立资产连接',
    operationFailed: '操作失败',
    noOperableMessage: '没有可操作的消息',
    commandCopied: '命令已复制',
    commandCopiedToClipboard: '命令已复制到剪贴板',
    executionCompleted: '执行完成',
    commandExecutionFailed: '命令执行失败',
    localCommandExecutionError: '本地命令执行出错',
    cannotExecuteCommand: '无法执行命令',
    wrongServerWindow:
      '当前窗口不是执行命令的服务器窗口。\n目标服务器: {targetServer}\n当前窗口: {currentWindow}\n\n请切换到正确的服务器窗口后再执行命令。',
    nonTerminalWindow: '非终端窗口',
    commandExecutedOnLocalhost: '在本地主机执行的命令',
    timeoutGettingAssetInfo: '获取资产信息超时',
    guestModeModelTip: '访客模式下使用默认模型，登录后可配置更多大模型提供商'
  },
  keyChain: {
    newKey: '添加密钥',
    keyDrop: '拖拽私钥文件到此处导入',
    editKey: '编辑密钥',
    saveKey: '保存密钥',
    createKey: '创建密钥',
    deleteConfirm: '删除确认',
    deleteConfirmContent: '确定要删除密钥 "{name}" 吗？',
    privateKey: '私钥',
    publicKey: '公钥',
    passphrase: '私钥的密码',
    alias: '别名',
    key: '密钥',
    pleaseInput: '请输入',
    name: '名称',
    type: '类型：',
    deleteSuccess: '删除密钥 {name} 成功',
    deleteFailure: '删除失败',
    deleteError: '删除出错: {error}',
    createSuccess: '创建密钥成功',
    saveSuccess: '保存成功',
    saveError: '保存出错',
    getKeyListFailed: '获取秘钥列表失败',
    createError: '创建出错',
    missingKeyId: '缺少密钥 ID',
    keyFileImported: '密钥文件已导入',
    readFileFailed: '读取文件失败'
  },
  userInfo: {
    enterprise: '企业用户',
    personal: '个人用户',
    vip: 'VIP用户',
    name: '名称',
    username: '用户名',
    mobile: '手机',
    email: '邮箱',
    organization: '组织',
    ip: 'IP地址',
    macAddress: 'Mac地址',
    password: '密码',
    pleaseInputName: '请输入名称',
    pleaseInputUsername: '请输入用户名',
    pleaseInputMobile: '请输入手机号',
    emailPlaceholder: '请输入邮箱地址',
    pleaseInputNewPassword: '请输入新密码',
    nameRequired: '名称不得为空',
    nameTooLong: '名称长度不能超过20位',
    usernameLengthError: '用户名长度需在6-20位之间',
    usernameFormatError: '用户名只能包含字母、数字和下划线',
    mobileInvalid: '请输入有效的手机号',
    passwordLengthError: '密码长度不能小于6位',
    passwordStrengthError: '请具有弱以上的密码强度',
    passwordStrength: '密码强度',
    passwordStrengthWeak: '弱',
    passwordStrengthMedium: '中',
    passwordStrengthStrong: '强',
    updateSuccess: '更新成功',
    updateFailed: '更新失败',
    passwordResetSuccess: '密码重置成功',
    passwordResetFailed: '密码重置失败',
    edit: '编辑',
    save: '保存',
    cancel: '取消',
    resetPassword: '重置密码',
    expirationTime: '过期时间',
    enterpriseCertification: '企业认证',
    guestModeNoPasswordChange: '访客模式下无法修改密码'
  },
  update: {
    available: '有最新版本可以更新',
    update: '更新',
    later: '稍后',
    downloading: '正在下载更新',
    complete: '已下载完毕，是否立即安装',
    install: '安装',
    clickUpdate: '点击重启以更新'
  },
  files: {
    name: '名称',
    permissions: '权限',
    size: '大小',
    modifyDate: '修改日期',
    uploadDirectory: '上传文件夹',
    uploadFile: '上传文件',
    rollback: '返回',
    moveTo: '移动到',
    cpTo: '复制到',
    originPath: '原路径',
    targetPath: '目标路径',
    pathInputTips: '请输入目标路径，如 /root/tmp',
    noDirTips: '暂无子目录',
    dirEdit: '编辑',
    conflictTips: '目标目录已存在同名文件',
    newFileName: '新文件名',
    rename: '重命名',
    overwrite: '覆盖',
    overwriteTips: '是否覆盖或以新名称保存',
    file: '文件',
    exists: '已存在于',
    deleteFileTips: '确认要删除以下文件吗？',
    deleting: '删除中...',
    deleteSuccess: '删除成功',
    deleteFailed: '删除失败',
    deleteError: '删除异常',
    modifySuccess: '修改成功',
    modifyFailed: '修改失败',
    modifyError: '修改异常',
    downloading: '下载中...',
    downloadSuccess: '下载成功',
    downloadFailed: '下载失败',
    downloadError: '下载异常',
    uploading: '上传中...',
    uploadSuccess: '上传成功',
    uploadFailed: '上传失败',
    uploadError: '上传异常',
    copyFileSuccess: '复制文件成功',
    copyFileFailed: '复制文件失败',
    copyFileError: '复制文件失败',
    moveFileSuccess: '移动文件成功',
    moveFileFailed: '移动文件失败',
    moveFileError: '移动文件异常',
    modifyFilePermissionsSuccess: '修改文件权限成功',
    modifyFilePermissionsFailed: '修改文件权限失败',
    modifyFilePermissionsError: '修改文件权限异常',
    read: '读取',
    write: '写入',
    exec: '执行',
    applyToSubdirectories: '应用到子目录',
    publicGroup: '公共组',
    userGroups: '用户组',
    owner: '所有者',
    permissionSettings: '权限设置',
    delete: '删除',
    move: '移动',
    copy: '复制',
    more: '更多',
    download: '下载',
    doubleClickToOpen: '双击打开',
    sftpConnectFailed: 'SFTP连接失败',
    pleaseInputNewFileName: '请输入新文件名'
  },
  about: {
    version: '版本',
    checkUpdate: '检查更新',
    latestVersion: '最新版本',
    downLoadUpdate: '下载更新',
    downloading: '正在下载',
    checkUpdateError: '检查更新失败',
    checking: '检查中',
    install: '安装'
  },
  mfa: {
    title: '二次验证',
    verificationCode: '验证码',
    verificationCodeError: '验证码错误',
    pleaseInputVerificationCode: '请输入验证码',
    remainingTime: '剩余时间',
    confirm: '确认',
    cancel: '取消',
    setupGlobalListeners: '设置全局 MFA 监听器'
  },
  ssh: {
    disconnected: '已断开连接。',
    pressEnterToReconnect: '按 Enter 键重新连接...',
    disconnectError: '断开连接错误: {message}',
    unknownError: '未知错误',
    connectionFailed: '连接失败: {message}',
    connectionError: '连接错误: {message}',
    shellStartFailed: '启动Shell失败: {message}',
    shellError: 'Shell错误: {message}',
    connectionClosed: '连接已关闭',
    disconnectedFromHost: '与远程主机({host})断开连接于 {date}',
    pressEnterToReconnectEn: '按 Enter 键重新连接...',
    connectingTo: '连接到 {ip}',
    welcomeMessage: '欢迎使用 RS - Chaterm智能终端',
    reconnecting: '正在重新连接...',
    terminalConnectionError: '连接错误。请检查终端服务器是否运行。'
  },
  quickCommand: {
    scriptName: '脚本名称',
    scriptContent: '请输入脚本内容...',
    scriptSyntaxHelp: '📖 脚本语法说明',
    basicCommands: '⚡ 基本命令：',
    basicCommandsDesc: '每行一个命令，按顺序执行',
    delayCommand: '⏰ 延时命令：',
    delayCommandDesc: '如：',
    specialKeys: '⌨️ 特殊按键：',
    exampleScript: '💡 示例脚本',
    copy: '复制',
    copied: '已复制',
    copyFailed: '复制失败',
    seconds: '秒数'
  }
}
