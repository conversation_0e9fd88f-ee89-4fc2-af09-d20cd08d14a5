<template>
  <div class="sync-configuration">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <Settings class="title-icon" />
          同步配置
        </h1>
        <p class="page-description">配置数据同步后端、策略和故障转移规则</p>
      </div>
      <div class="header-actions">
        <a-button
          type="primary"
          @click="showAddBackendModal"
        >
          <Plus class="btn-icon" />
          添加后端
        </a-button>
      </div>
    </div>

    <!-- 配置概览 -->
    <div class="config-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="配置后端总数"
              :value="backends.length"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <Database class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="启用的后端"
              :value="enabledBackends.length"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircle class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="健康后端"
              :value="healthyBackends.length"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <Heart class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="最后同步"
              :value="lastSyncTime"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <Clock class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 后端配置列表 -->
    <div class="backend-list">
      <div class="section-header">
        <h2 class="section-title">配置后端</h2>
        <div class="section-actions">
          <a-button
            :loading="testingAll"
            @click="testAllBackends"
          >
            <TestTube class="btn-icon" />
            测试所有连接
          </a-button>
          <a-button
            :loading="syncingAll"
            @click="syncAllBackends"
          >
            <RefreshCw class="btn-icon" />
            同步所有后端
          </a-button>
        </div>
      </div>

      <div class="backend-cards">
        <div
          v-for="backend in backends"
          :key="backend.id"
          class="backend-card"
          :class="{ disabled: !backend.enabled }"
        >
          <div class="card-header">
            <div class="backend-info">
              <div class="backend-icon">
                <Database v-if="backend.type === 'database'" />
                <HardDrive v-else-if="backend.type === 'smb'" />
                <GitBranch v-else-if="backend.type === 'git'" />
                <Server v-else-if="backend.type === 'consul'" />
                <Cloud v-else />
              </div>
              <div class="backend-details">
                <h3 class="backend-name">{{ backend.name }}</h3>
                <p class="backend-type">{{ getBackendTypeLabel(backend.type) }}</p>
              </div>
            </div>
            <div class="backend-status">
              <a-badge
                :status="getHealthStatusType(backend.health_status)"
                :text="getHealthStatusText(backend.health_status)"
              />
              <a-switch
                v-model:checked="backend.enabled"
                size="small"
                @change="toggleBackend(backend)"
              />
            </div>
          </div>

          <div class="card-content">
            <div class="backend-meta">
              <div class="meta-item">
                <span class="meta-label">优先级:</span>
                <a-tag color="blue">{{ backend.priority }}</a-tag>
              </div>
              <div class="meta-item">
                <span class="meta-label">最后检查:</span>
                <span class="meta-value">{{ formatTime(backend.last_health_check) }}</span>
              </div>
            </div>
            <div class="backend-config">
              <a-descriptions
                size="small"
                :column="1"
              >
                <a-descriptions-item
                  v-for="(value, key) in getDisplayConfig(backend.config)"
                  :key="key"
                  :label="key"
                >
                  {{ value }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </div>

          <div class="card-actions">
            <a-button
              size="small"
              :loading="backend.testing"
              @click="testBackend(backend)"
            >
              <TestTube class="action-icon" />
              测试连接
            </a-button>
            <a-button
              size="small"
              :loading="backend.syncing"
              @click="syncBackend(backend)"
            >
              <RefreshCw class="action-icon" />
              立即同步
            </a-button>
            <a-button
              size="small"
              @click="editBackend(backend)"
            >
              <Edit class="action-icon" />
              编辑
            </a-button>
            <a-button
              size="small"
              danger
              @click="deleteBackend(backend)"
            >
              <Trash2 class="action-icon" />
              删除
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 同步策略配置 -->
    <div class="sync-strategy">
      <div class="section-header">
        <h2 class="section-title">同步策略</h2>
      </div>

      <a-card>
        <a-form
          :model="syncStrategy"
          layout="vertical"
        >
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="同步间隔">
                <a-select v-model:value="syncStrategy.interval">
                  <a-select-option value="300">5分钟</a-select-option>
                  <a-select-option value="600">10分钟</a-select-option>
                  <a-select-option value="1800">30分钟</a-select-option>
                  <a-select-option value="3600">1小时</a-select-option>
                  <a-select-option value="21600">6小时</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="冲突解决策略">
                <a-select v-model:value="syncStrategy.conflictResolution">
                  <a-select-option value="server_wins">服务器优先</a-select-option>
                  <a-select-option value="client_wins">客户端优先</a-select-option>
                  <a-select-option value="timestamp">时间戳优先</a-select-option>
                  <a-select-option value="manual">手动解决</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="重试次数">
                <a-input-number
                  v-model:value="syncStrategy.retryCount"
                  :min="0"
                  :max="10"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item>
                <a-checkbox v-model:checked="syncStrategy.autoSync"> 启用自动同步 </a-checkbox>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item>
                <a-checkbox v-model:checked="syncStrategy.enableFailover"> 启用故障转移 </a-checkbox>
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item>
            <a-button
              type="primary"
              @click="saveSyncStrategy"
            >
              保存策略配置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 添加/编辑后端模态框 -->
    <a-modal
      v-model:open="showBackendModal"
      :title="editingBackend ? '编辑后端' : '添加后端'"
      width="800px"
      @ok="handleSaveBackend"
      @cancel="handleCancelBackend"
    >
      <a-form
        ref="backendForm"
        :model="backendFormData"
        :rules="backendFormRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="后端名称"
              name="name"
            >
              <a-input
                v-model:value="backendFormData.name"
                placeholder="请输入后端名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="后端类型"
              name="type"
            >
              <a-select
                v-model:value="backendFormData.type"
                placeholder="请选择后端类型"
                @change="handleTypeChange"
              >
                <a-select-option value="database">数据库</a-select-option>
                <a-select-option value="smb">SMB共享</a-select-option>
                <a-select-option value="git">Git仓库</a-select-option>
                <a-select-option value="consul">Consul</a-select-option>
                <a-select-option value="s3">对象存储</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="优先级"
              name="priority"
            >
              <a-input-number
                v-model:value="backendFormData.priority"
                :min="1"
                :max="100"
                style="width: 100%"
                placeholder="数字越小优先级越高"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <a-checkbox v-model:checked="backendFormData.enabled"> 启用此后端 </a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 动态配置表单 -->
        <div
          v-if="backendFormData.type"
          class="config-form"
        >
          <h4>{{ getBackendTypeLabel(backendFormData.type) }}配置</h4>

          <!-- 数据库配置 -->
          <template v-if="backendFormData.type === 'database'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="数据库类型">
                  <a-select v-model:value="backendFormData.config.database_type">
                    <a-select-option value="postgresql">PostgreSQL</a-select-option>
                    <a-select-option value="mysql">MySQL</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="表名">
                  <a-input
                    v-model:value="backendFormData.config.table_name"
                    placeholder="host_configurations"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="连接字符串">
              <a-input
                v-model:value="backendFormData.config.connection_string"
                placeholder="host=localhost port=5432 dbname=config"
              />
            </a-form-item>
          </template>

          <!-- SMB配置 -->
          <template v-if="backendFormData.type === 'smb'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="主机地址">
                  <a-input
                    v-model:value="backendFormData.config.host"
                    placeholder="fileserver.company.com"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="共享名">
                  <a-input
                    v-model:value="backendFormData.config.share"
                    placeholder="it_resources"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户名">
                  <a-input
                    v-model:value="backendFormData.config.username"
                    placeholder="service_account"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="域">
                  <a-input
                    v-model:value="backendFormData.config.domain"
                    placeholder="COMPANY"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="文件路径">
              <a-input
                v-model:value="backendFormData.config.path"
                placeholder="/configs/hosts.yaml"
              />
            </a-form-item>
          </template>

          <!-- Git配置 -->
          <template v-if="backendFormData.type === 'git'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="仓库地址">
                  <a-input
                    v-model:value="backendFormData.config.repository"
                    placeholder="**************:company/config.git"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="分支">
                  <a-input
                    v-model:value="backendFormData.config.branch"
                    placeholder="main"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="文件路径">
              <a-input
                v-model:value="backendFormData.config.file_path"
                placeholder="hosts.yaml"
              />
            </a-form-item>
          </template>

          <!-- Consul配置 -->
          <template v-if="backendFormData.type === 'consul'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="主机地址">
                  <a-input
                    v-model:value="backendFormData.config.host"
                    placeholder="consul.company.com"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="端口">
                  <a-input-number
                    v-model:value="backendFormData.config.port"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                    placeholder="8500"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="键前缀">
              <a-input
                v-model:value="backendFormData.config.key_prefix"
                placeholder="chaterm/hosts/"
              />
            </a-form-item>
          </template>

          <!-- S3配置 -->
          <template v-if="backendFormData.type === 's3'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="端点">
                  <a-input
                    v-model:value="backendFormData.config.endpoint"
                    placeholder="s3.amazonaws.com"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="存储桶">
                  <a-input
                    v-model:value="backendFormData.config.bucket"
                    placeholder="config-bucket"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="访问密钥">
                  <a-input
                    v-model:value="backendFormData.config.access_key"
                    placeholder="AKIAIOSFODNN7EXAMPLE"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="密钥">
                  <a-input-password
                    v-model:value="backendFormData.config.secret_key"
                    placeholder="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </template>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, notification } from 'ant-design-vue'
import {
  Settings,
  Plus,
  Database,
  CheckCircle,
  Heart,
  Clock,
  TestTube,
  RefreshCw,
  HardDrive,
  GitBranch,
  Server,
  Cloud,
  Edit,
  Trash2
} from 'lucide-vue-next'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

/**
 * 同步配置页面
 * 功能：配置数据同步后端、策略和故障转移规则
 * 依赖：Ant Design Vue、Lucide Vue Next、date-fns
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

// 响应式数据
const testingAll = ref(false)
const syncingAll = ref(false)
const showBackendModal = ref(false)
const editingBackend = ref(null)

// 后端列表数据
const backends = ref([
  {
    id: '1',
    name: 'primary-db',
    type: 'database',
    priority: 1,
    enabled: true,
    health_status: 'healthy',
    last_health_check: new Date('2025-01-15T10:30:00Z'),
    config: {
      database_type: 'postgresql',
      connection_string: 'host=localhost port=5432 dbname=config',
      table_name: 'host_configurations'
    },
    testing: false,
    syncing: false
  },
  {
    id: '2',
    name: 'legacy-smb',
    type: 'smb',
    priority: 2,
    enabled: true,
    health_status: 'unhealthy',
    last_health_check: new Date('2025-01-15T09:15:00Z'),
    config: {
      host: 'fileserver.company.com',
      share: 'it_resources',
      path: '/configs/hosts.yaml',
      username: 'service_account',
      domain: 'COMPANY'
    },
    testing: false,
    syncing: false
  }
])

// 同步策略配置
const syncStrategy = reactive({
  interval: 1800,
  conflictResolution: 'timestamp',
  retryCount: 3,
  autoSync: true,
  enableFailover: true
})

// 表单数据
const backendFormData = reactive({
  name: '',
  type: '',
  priority: 1,
  enabled: true,
  config: {}
})

// 表单验证规则
const backendFormRules = {
  name: [{ required: true, message: '请输入后端名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择后端类型', trigger: 'change' }],
  priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }]
}

// 计算属性
const enabledBackends = computed(() => {
  return backends.value.filter((backend) => backend.enabled)
})

const healthyBackends = computed(() => {
  return backends.value.filter((backend) => backend.health_status === 'healthy')
})

const lastSyncTime = computed(() => {
  const times = backends.value.map((b) => b.last_health_check).filter(Boolean)
  if (times.length === 0) return '从未同步'
  const latest = new Date(Math.max(...times.map((t) => t.getTime())))
  return formatDistanceToNow(latest, { addSuffix: true, locale: zhCN })
})

// 方法
const formatTime = (date: Date) => {
  if (!date) return '从未检查'
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

const getBackendTypeLabel = (type: string) => {
  const labels = {
    database: '数据库',
    smb: 'SMB共享',
    git: 'Git仓库',
    consul: 'Consul',
    s3: '对象存储'
  }
  return labels[type] || type
}

const getHealthStatusType = (status: string) => {
  const types = {
    healthy: 'success',
    unhealthy: 'error',
    unknown: 'default'
  }
  return types[status] || 'default'
}

const getHealthStatusText = (status: string) => {
  const texts = {
    healthy: '健康',
    unhealthy: '异常',
    unknown: '未知'
  }
  return texts[status] || status
}

const getDisplayConfig = (config: any) => {
  const display = {}
  for (const [key, value] of Object.entries(config)) {
    if (key.includes('password') || key.includes('secret') || key.includes('key')) {
      display[key] = '***'
    } else {
      display[key] = String(value)
    }
  }
  return display
}

const showAddBackendModal = () => {
  editingBackend.value = null
  resetBackendForm()
  showBackendModal.value = true
}

const editBackend = (backend: any) => {
  editingBackend.value = backend
  Object.assign(backendFormData, backend)
  showBackendModal.value = true
}

const deleteBackend = (backend: any) => {
  const index = backends.value.findIndex((b) => b.id === backend.id)
  if (index > -1) {
    backends.value.splice(index, 1)
    message.success('后端删除成功')
  }
}

const toggleBackend = (backend: any) => {
  message.success(`后端已${backend.enabled ? '启用' : '禁用'}`)
}

const testBackend = async (backend: any) => {
  backend.testing = true
  try {
    // 模拟测试连接
    await new Promise((resolve) => setTimeout(resolve, 2000))
    backend.health_status = 'healthy'
    backend.last_health_check = new Date()
    message.success(`${backend.name} 连接测试成功`)
  } catch (error) {
    backend.health_status = 'unhealthy'
    message.error(`${backend.name} 连接测试失败`)
  } finally {
    backend.testing = false
  }
}

const syncBackend = async (backend: any) => {
  backend.syncing = true
  try {
    // 模拟同步操作
    await new Promise((resolve) => setTimeout(resolve, 3000))
    message.success(`${backend.name} 同步完成`)
  } catch (error) {
    message.error(`${backend.name} 同步失败`)
  } finally {
    backend.syncing = false
  }
}

const testAllBackends = async () => {
  testingAll.value = true
  try {
    const promises = enabledBackends.value.map((backend) => testBackend(backend))
    await Promise.all(promises)
    message.success('所有后端连接测试完成')
  } finally {
    testingAll.value = false
  }
}

const syncAllBackends = async () => {
  syncingAll.value = true
  try {
    const promises = enabledBackends.value.map((backend) => syncBackend(backend))
    await Promise.all(promises)
    message.success('所有后端同步完成')
  } finally {
    syncingAll.value = false
  }
}

const handleTypeChange = () => {
  // 重置配置对象
  backendFormData.config = {}
}

const resetBackendForm = () => {
  Object.assign(backendFormData, {
    name: '',
    type: '',
    priority: 1,
    enabled: true,
    config: {}
  })
}

const handleSaveBackend = () => {
  if (editingBackend.value) {
    // 更新现有后端
    Object.assign(editingBackend.value, backendFormData)
    message.success('后端配置更新成功')
  } else {
    // 添加新后端
    const newBackend = {
      ...backendFormData,
      id: Date.now().toString(),
      health_status: 'unknown',
      last_health_check: null,
      testing: false,
      syncing: false
    }
    backends.value.push(newBackend)
    message.success('后端配置添加成功')
  }
  showBackendModal.value = false
}

const handleCancelBackend = () => {
  showBackendModal.value = false
  resetBackendForm()
}

const saveSyncStrategy = () => {
  // 保存同步策略配置
  message.success('同步策略配置保存成功')
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.sync-configuration {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  margin-right: 12px;
  color: #1890ff;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.config-overview {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.stat-icon {
  width: 20px;
  height: 20px;
}

.backend-list {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.section-actions {
  display: flex;
  gap: 12px;
}

.backend-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
}

.backend-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s;
}

.backend-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
}

.backend-card.disabled {
  opacity: 0.6;
  background: #f9f9f9;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.backend-info {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.backend-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
  margin-right: 12px;
  color: #1890ff;
}

.backend-details {
  flex: 1;
}

.backend-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.backend-type {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.backend-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.card-content {
  margin-bottom: 16px;
}

.backend-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-label {
  color: #6b7280;
  font-size: 12px;
}

.meta-value {
  color: #1f2937;
  font-size: 12px;
}

.backend-config {
  background: #f9fafb;
  border-radius: 6px;
  padding: 12px;
}

.card-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.sync-strategy {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.config-form {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.config-form h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}
</style>
