// 登录认证功能已屏蔽 - Login authentication functionality disabled
export const AppRoutes = [
  {
    path: '/',
    name: 'Home',
    meta: {
      requiresAuth: false // 已屏蔽认证要求 - Authentication requirement disabled
    },
    component: () => import('@/views/index.vue')
  },
  {
    path: '/optimization-demo',
    name: 'OptimizationDemo',
    meta: {
      requiresAuth: false
    },
    component: () => import('@/views/OptimizationDemo.vue')
  },
  // 企业资源管理路由
  {
    path: '/resources/personal',
    name: 'PersonalResources',
    meta: {
      requiresAuth: false,
      title: '个人资源管理'
    },
    component: () => import('@/views/resources/PersonalResourceManagement.vue')
  },
  {
    path: '/resources/enterprise',
    name: 'EnterpriseResources',
    meta: {
      requiresAuth: false,
      title: '企业资源管理'
    },
    component: () => import('@/views/enterprise/EnterpriseResourceManagement.vue')
  },
  // 同步配置路由
  {
    path: '/sync/config',
    name: 'SyncConfig',
    meta: {
      requiresAuth: false,
      title: '同步配置'
    },
    component: () => import('@/views/sync/SyncConfiguration.vue')
  },
  {
    path: '/sync/monitor',
    name: 'SyncMonitor',
    meta: {
      requiresAuth: false,
      title: '监控仪表板'
    },
    component: () => import('@/views/sync/SyncMonitor.vue')
  },
  // 安全管理路由
  {
    path: '/security/permissions',
    name: 'SecurityPermissions',
    meta: {
      requiresAuth: false,
      title: '权限管理'
    },
    component: () => import('@/views/security/PermissionManagement.vue')
  },
  {
    path: '/security/audit',
    name: 'SecurityAudit',
    meta: {
      requiresAuth: false,
      title: '审计日志'
    },
    component: () => import('@/views/security/AuditLog.vue')
  },
  // 系统设置路由
  {
    path: '/settings/global',
    name: 'GlobalSettings',
    meta: {
      requiresAuth: false,
      title: '全局设置'
    },
    component: () => import('@/views/settings/GlobalSettings.vue')
  },
  {
    path: '/settings/users',
    name: 'UserManagement',
    meta: {
      requiresAuth: false,
      title: '用户管理'
    },
    component: () => import('@/views/settings/UserManagement.vue')
  }
  // 登录路由已移除 - Login route removed
  // {
  //   path: '/login',
  //   name: 'Login',
  //   meta: {
  //     requiresAuth: false
  //   },
  //   component: () => import('@/views/auth/login.vue')
  // }
]
