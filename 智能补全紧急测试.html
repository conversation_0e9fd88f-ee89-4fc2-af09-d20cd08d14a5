<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能补全紧急测试</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-panel {
            background: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-panel h3 {
            color: #569cd6;
            margin-top: 0;
        }
        .btn {
            background: #0e639c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .btn:hover {
            background: #1177bb;
        }
        .btn.success {
            background: #16825d;
        }
        .btn.warning {
            background: #ca5010;
        }
        .btn.large {
            padding: 16px 32px;
            font-size: 18px;
            font-weight: bold;
        }
        .log-output {
            background: #0c0c0c;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .status {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 16px;
        }
        .status.success {
            background: #16825d;
            color: white;
        }
        .status.error {
            background: #a1260d;
            color: white;
        }
        .status.warning {
            background: #ca5010;
            color: white;
        }
        .instructions {
            background: #264f78;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .instructions h2 {
            margin-top: 0;
            color: #ffffff;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 智能补全紧急测试</h1>
        
        <div class="instructions">
            <h2>📋 测试说明</h2>
            <div class="step">
                <strong>步骤1:</strong> 确保您已经打开了 Chaterm 应用并连接到终端
            </div>
            <div class="step">
                <strong>步骤2:</strong> 在 Chaterm 的终端页面按 F12 打开开发者工具
            </div>
            <div class="step">
                <strong>步骤3:</strong> 点击下面的测试按钮，然后查看 Chaterm 终端是否显示智能补全弹窗
            </div>
            <div class="step">
                <strong>步骤4:</strong> 如果看到绿色的"测试智能补全"按钮，点击它进行测试
            </div>
        </div>
        
        <div class="test-panel">
            <h3>🎯 核心测试</h3>
            <div id="systemStatus"></div>
            <button class="btn large success" onclick="runMainTest()">🚀 运行主要测试</button>
            <button class="btn large" onclick="runDebugTest()">🔍 运行调试测试</button>
            <button class="btn large warning" onclick="runForceTest()">⚡ 强制显示测试</button>
        </div>

        <div class="test-panel">
            <h3>📊 详细测试</h3>
            <button class="btn" onclick="testFunction('debugTerminalState')">检查终端状态</button>
            <button class="btn" onclick="testFunction('enableSmartCompletion')">启用智能补全</button>
            <button class="btn" onclick="testFunction('forceShowSuggestions')">强制显示建议</button>
            <button class="btn" onclick="testFunction('fullSmartCompletionTest')">完整补全测试</button>
            <button class="btn" onclick="testFunction('testDirectCompletion')">直接补全测试</button>
        </div>

        <div class="test-panel">
            <h3>📝 测试日志</h3>
            <button class="btn warning" onclick="clearLogs()">清空日志</button>
            <div id="logOutput" class="log-output"></div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('logOutput');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logContainer.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            logContainer.textContent = '';
        }

        function runMainTest() {
            log('🚀 开始运行主要测试...', 'info');
            
            // 检查基本函数
            const functions = ['debugTerminalState', 'forceShowSuggestions', 'fullSmartCompletionTest'];
            let availableCount = 0;
            
            functions.forEach(func => {
                if (window[func]) {
                    log(`✅ ${func} 函数可用`, 'success');
                    availableCount++;
                } else {
                    log(`❌ ${func} 函数不可用`, 'error');
                }
            });
            
            if (availableCount === functions.length) {
                log('🎉 所有测试函数都可用，开始执行测试...', 'success');
                
                // 执行测试序列
                setTimeout(() => {
                    log('1️⃣ 检查终端状态...', 'info');
                    window.debugTerminalState();
                }, 500);
                
                setTimeout(() => {
                    log('2️⃣ 强制显示建议...', 'info');
                    window.forceShowSuggestions();
                }, 1500);
                
                setTimeout(() => {
                    log('3️⃣ 执行完整测试...', 'info');
                    window.fullSmartCompletionTest();
                }, 2500);
                
                setTimeout(() => {
                    log('🏁 测试完成！请查看 Chaterm 终端是否显示智能补全弹窗', 'success');
                }, 3500);
                
            } else {
                log('❌ 部分测试函数不可用，请确保已连接到 Chaterm 终端', 'error');
            }
        }

        function runDebugTest() {
            log('🔍 开始运行调试测试...', 'info');
            
            if (window.debugTerminalState) {
                window.debugTerminalState();
                log('✅ 已执行 debugTerminalState，请查看控制台输出', 'success');
            } else {
                log('❌ debugTerminalState 函数不可用', 'error');
            }
        }

        function runForceTest() {
            log('⚡ 开始强制显示测试...', 'info');
            
            if (window.forceShowSuggestions) {
                window.forceShowSuggestions();
                log('✅ 已执行 forceShowSuggestions，请查看 Chaterm 终端', 'success');
            } else {
                log('❌ forceShowSuggestions 函数不可用', 'error');
            }
        }

        function testFunction(funcName) {
            log(`🧪 测试函数: ${funcName}`, 'info');
            
            if (window[funcName]) {
                try {
                    if (funcName === 'testDirectCompletion') {
                        window[funcName]('ls');
                    } else {
                        window[funcName]();
                    }
                    log(`✅ ${funcName} 执行成功`, 'success');
                } catch (error) {
                    log(`❌ ${funcName} 执行失败: ${error.message}`, 'error');
                }
            } else {
                log(`❌ ${funcName} 函数不可用`, 'error');
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            log('📱 智能补全紧急测试页面已加载', 'info');
            log('📋 请按照说明进行测试', 'info');
            
            // 检查基本状态
            setTimeout(() => {
                const statusDiv = document.getElementById('systemStatus');
                const hasDebugFunc = !!window.debugTerminalState;
                const hasForceFunc = !!window.forceShowSuggestions;
                
                if (hasDebugFunc && hasForceFunc) {
                    statusDiv.innerHTML = '<div class="status success">🎉 检测到 Chaterm 调试函数，可以开始测试！</div>';
                } else if (hasDebugFunc || hasForceFunc) {
                    statusDiv.innerHTML = '<div class="status warning">⚠️ 部分调试函数可用，请确保已连接到终端</div>';
                } else {
                    statusDiv.innerHTML = '<div class="status error">❌ 未检测到调试函数，请确保已打开 Chaterm 并连接到终端</div>';
                }
            }, 1000);
        });
    </script>
</body>
</html>
